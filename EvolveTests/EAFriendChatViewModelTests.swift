import XCTest
import SwiftData
@testable import Evolve

/// EAFriendChatViewModel 单元测试
/// 验证MVVM重构后的ViewModel功能正确性
final class EAFriendChatViewModelTests: XCTestCase {
    
    var viewModel: EAFriendChatViewModel!
    var mockContainer: ModelContainer!
    var mockRepositoryContainer: EARepositoryContainer!
    var mockSessionManager: EASessionManager!
    var mockChatService: EAFriendChatService!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // 创建测试用的ModelContainer
        mockContainer = try EAAppSchema.createPreviewContainer()
        mockRepositoryContainer = EARepositoryContainerImpl(modelContainer: mockContainer)
        mockSessionManager = EASessionManager()
        
        // 创建测试用的AI数据桥接
        let aiDataBridge = EACommunityAIDataBridge(repositoryContainer: mockRepositoryContainer)
        mockChatService = EAFriendChatService(
            repositoryContainer: mockRepositoryContainer,
            sessionManager: mockSessionManager,
            aiDataBridge: aiDataBridge
        )
        
        // 创建ViewModel实例
        let testFriendshipId = UUID()
        let testCurrentUserID = UUID()
        
        viewModel = EAFriendChatViewModel(
            friendshipId: testFriendshipId,
            currentUserID: testCurrentUserID,
            chatService: mockChatService,
            repositoryContainer: mockRepositoryContainer,
            sessionManager: mockSessionManager
        )
    }
    
    override func tearDown() async throws {
        viewModel = nil
        mockChatService = nil
        mockSessionManager = nil
        mockRepositoryContainer = nil
        mockContainer = nil
        try await super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    /// 测试ViewModel初始化
    func testViewModelInitialization() {
        XCTAssertNotNil(viewModel)
        XCTAssertEqual(viewModel.messages.count, 0)
        XCTAssertEqual(viewModel.messageText, "")
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertFalse(viewModel.isSendingMessage)
        XCTAssertFalse(viewModel.hasError)
        XCTAssertFalse(viewModel.showAlert)
    }
    
    /// 测试错误状态管理
    func testErrorStateManagement() async {
        // 测试显示错误
        await viewModel.showError("测试错误")
        
        XCTAssertTrue(viewModel.hasError)
        XCTAssertTrue(viewModel.showAlert)
        XCTAssertEqual(viewModel.errorMessage, "测试错误")
        
        // 测试清除错误
        viewModel.clearError()
        
        XCTAssertFalse(viewModel.hasError)
        XCTAssertFalse(viewModel.showAlert)
        XCTAssertNil(viewModel.errorMessage)
    }
    
    /// 测试消息发送状态
    func testCanSendMessage() {
        // 初始状态不能发送
        XCTAssertFalse(viewModel.canSendMessage)
        
        // 设置消息文本
        viewModel.messageText = "测试消息"
        // 仍然不能发送，因为没有friendship
        XCTAssertFalse(viewModel.canSendMessage)
        
        // 清空消息文本
        viewModel.messageText = ""
        XCTAssertFalse(viewModel.canSendMessage)
        
        // 只有空白字符
        viewModel.messageText = "   "
        XCTAssertFalse(viewModel.canSendMessage)
    }
    
    /// 测试数据完整性验证
    func testDataIntegrityValidation() async {
        let result = await viewModel.validateChatDataIntegrity()
        
        // 由于没有真实的用户会话，应该返回false
        XCTAssertFalse(result)
        XCTAssertTrue(viewModel.hasError)
    }
    
    // MARK: - 状态管理测试
    
    /// 测试加载状态
    func testLoadingStates() {
        // 测试初始加载状态
        XCTAssertFalse(viewModel.isInitialLoading)
        
        // 测试数据验证状态
        XCTAssertFalse(viewModel.isValidatingDataIntegrity)
        
        // 测试普通加载状态
        XCTAssertFalse(viewModel.isLoading)
    }
    
    /// 测试屏蔽状态
    func testBlockingStates() {
        // 初始状态
        XCTAssertFalse(viewModel.isBlocked)
        XCTAssertFalse(viewModel.hasBlockedFriend)
        XCTAssertFalse(viewModel.showUnblockConfirmation)
    }
    
    /// 测试用户档案状态
    func testUserProfileStates() {
        // 初始状态
        XCTAssertNil(viewModel.friendProfile)
        XCTAssertNil(viewModel.currentUserProfile)
        XCTAssertNil(viewModel.currentUserAvatarData)
        XCTAssertNil(viewModel.friendAvatarData)
        XCTAssertEqual(viewModel.friendDisplayName, "")
    }
}

// MARK: - 测试扩展

extension EAFriendChatViewModelTests {
    
    /// 创建测试用的好友关系
    private func createTestFriendship() -> EAFriendship {
        let friendship = EAFriendship()
        // 这里可以设置测试数据
        return friendship
    }
    
    /// 创建测试用的用户
    private func createTestUser() -> EAUser {
        let user = EAUser(username: "testuser", email: "<EMAIL>")
        // 这里可以设置测试数据
        return user
    }
}
